/**
 * Outbox Routes for Archive Service
 * Internal routes for outbox event management and monitoring
 * Used by outbox publisher worker and monitoring systems
 */

const express = require('express');
const Joi = require('joi');
const OutboxService = require('../services/outboxService');
const { requireServiceAuth } = require('../middleware/auth');
const { validateBody, validateQuery, validateParams } = require('../middleware/validation');
const { sendSuccess, sendError } = require('../utils/responseFormatter');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const eventIdParamSchema = Joi.object({
  eventId: Joi.string().uuid().required()
});

const getEventsQuerySchema = Joi.object({
  limit: Joi.number().integer().min(1).max(1000).default(50),
  offset: Joi.number().integer().min(0).default(0),
  eventType: Joi.string().valid('assessment.submitted', 'assessment.completed', 'assessment.failed', 'result.created').optional(),
  aggregateType: Joi.string().valid('assessment_job', 'analysis_result', 'user_profile').optional(),
  aggregateId: Joi.string().uuid().optional()
});

const markProcessedSchema = Joi.object({
  eventIds: Joi.array().items(Joi.string().uuid()).min(1).max(100).required()
});

const markFailedSchema = Joi.object({
  eventId: Joi.string().uuid().required(),
  errorMessage: Joi.string().max(1000).required(),
  incrementRetry: Joi.boolean().default(true)
});

const cleanupSchema = Joi.object({
  olderThanDays: Joi.number().integer().min(1).max(365).default(30)
});

/**
 * GET /outbox/events/unprocessed
 * Get unprocessed outbox events for publishing
 */
router.get('/events/unprocessed',
  requireServiceAuth,
  validateQuery(getEventsQuerySchema),
  async (req, res, next) => {
    try {
      const { limit, offset, eventType } = req.query;

      const events = await OutboxService.getUnprocessedEvents({
        limit,
        offset,
        eventType
      });

      logger.info('Retrieved unprocessed outbox events', {
        count: events.length,
        limit,
        offset,
        eventType,
        requestedBy: req.serviceId
      });

      return sendSuccess(res, 'Unprocessed events retrieved successfully', {
        events,
        count: events.length,
        limit,
        offset
      });
    } catch (error) {
      logger.error('Failed to get unprocessed events', {
        error: error.message,
        query: req.query,
        requestedBy: req.serviceId
      });
      next(error);
    }
  }
);

/**
 * GET /outbox/events/retryable
 * Get events that can be retried
 */
router.get('/events/retryable',
  requireServiceAuth,
  validateQuery(getEventsQuerySchema),
  async (req, res, next) => {
    try {
      const { limit, minRetryDelay = 60000 } = req.query;

      const events = await OutboxService.getRetryableEvents({
        limit,
        minRetryDelay
      });

      logger.info('Retrieved retryable outbox events', {
        count: events.length,
        limit,
        minRetryDelay,
        requestedBy: req.serviceId
      });

      return sendSuccess(res, 'Retryable events retrieved successfully', {
        events,
        count: events.length,
        limit
      });
    } catch (error) {
      logger.error('Failed to get retryable events', {
        error: error.message,
        query: req.query,
        requestedBy: req.serviceId
      });
      next(error);
    }
  }
);

/**
 * GET /outbox/events/aggregate/:aggregateType/:aggregateId
 * Get events by aggregate
 */
router.get('/events/aggregate/:aggregateType/:aggregateId',
  requireServiceAuth,
  async (req, res, next) => {
    try {
      const { aggregateType, aggregateId } = req.params;

      const events = await OutboxService.getEventsByAggregate(aggregateType, aggregateId);

      logger.info('Retrieved events by aggregate', {
        aggregateType,
        aggregateId,
        count: events.length,
        requestedBy: req.serviceId
      });

      return sendSuccess(res, 'Events retrieved successfully', {
        events,
        count: events.length,
        aggregateType,
        aggregateId
      });
    } catch (error) {
      logger.error('Failed to get events by aggregate', {
        error: error.message,
        params: req.params,
        requestedBy: req.serviceId
      });
      next(error);
    }
  }
);

/**
 * PUT /outbox/events/processed
 * Mark events as processed (batch operation)
 */
router.put('/events/processed',
  requireServiceAuth,
  validateBody(markProcessedSchema),
  async (req, res, next) => {
    try {
      const { eventIds } = req.body;

      const results = {
        processed: 0,
        failed: 0,
        errors: []
      };

      for (const eventId of eventIds) {
        try {
          await OutboxService.markAsProcessed(eventId);
          results.processed++;
        } catch (error) {
          results.failed++;
          results.errors.push({
            eventId,
            error: error.message
          });
        }
      }

      logger.info('Batch mark as processed completed', {
        totalEvents: eventIds.length,
        processed: results.processed,
        failed: results.failed,
        requestedBy: req.serviceId
      });

      return sendSuccess(res, 'Events processed successfully', results);
    } catch (error) {
      logger.error('Failed to mark events as processed', {
        error: error.message,
        body: req.body,
        requestedBy: req.serviceId
      });
      next(error);
    }
  }
);

/**
 * PUT /outbox/events/:eventId/failed
 * Mark event as failed
 */
router.put('/events/:eventId/failed',
  requireServiceAuth,
  validateParams(eventIdParamSchema),
  validateBody(markFailedSchema),
  async (req, res, next) => {
    try {
      const { eventId } = req.params;
      const { errorMessage, incrementRetry } = req.body;

      await OutboxService.markAsFailed(eventId, errorMessage, incrementRetry);

      logger.info('Event marked as failed', {
        eventId,
        errorMessage,
        incrementRetry,
        requestedBy: req.serviceId
      });

      return sendSuccess(res, 'Event marked as failed successfully', {
        eventId,
        errorMessage,
        incrementRetry
      });
    } catch (error) {
      logger.error('Failed to mark event as failed', {
        error: error.message,
        params: req.params,
        body: req.body,
        requestedBy: req.serviceId
      });
      next(error);
    }
  }
);

/**
 * GET /outbox/stats
 * Get outbox processing statistics
 */
router.get('/stats',
  requireServiceAuth,
  async (req, res, next) => {
    try {
      const stats = await OutboxService.getProcessingStats();

      logger.debug('Retrieved outbox stats', {
        stats,
        requestedBy: req.serviceId
      });

      return sendSuccess(res, 'Statistics retrieved successfully', stats);
    } catch (error) {
      logger.error('Failed to get outbox stats', {
        error: error.message,
        requestedBy: req.serviceId
      });
      next(error);
    }
  }
);

/**
 * DELETE /outbox/cleanup
 * Clean up old processed events
 */
router.delete('/cleanup',
  requireServiceAuth,
  validateBody(cleanupSchema),
  async (req, res, next) => {
    try {
      const { olderThanDays } = req.body;

      const deletedCount = await OutboxService.cleanupOldEvents(olderThanDays);

      logger.info('Outbox cleanup completed', {
        deletedCount,
        olderThanDays,
        requestedBy: req.serviceId
      });

      return sendSuccess(res, 'Cleanup completed successfully', {
        deletedCount,
        olderThanDays
      });
    } catch (error) {
      logger.error('Failed to cleanup outbox events', {
        error: error.message,
        body: req.body,
        requestedBy: req.serviceId
      });
      next(error);
    }
  }
);

/**
 * GET /outbox/health
 * Health check for outbox system
 */
router.get('/health',
  requireServiceAuth,
  async (req, res, next) => {
    try {
      const stats = await OutboxService.getProcessingStats();
      
      // Check for potential issues
      const issues = [];
      
      if (stats.pending > 1000) {
        issues.push('High number of pending events');
      }
      
      if (stats.failed > stats.processed * 0.1) {
        issues.push('High failure rate');
      }
      
      if (stats.oldest_pending) {
        const oldestPending = new Date(stats.oldest_pending);
        const ageMinutes = (Date.now() - oldestPending.getTime()) / (1000 * 60);
        if (ageMinutes > 30) {
          issues.push(`Oldest pending event is ${Math.round(ageMinutes)} minutes old`);
        }
      }

      const isHealthy = issues.length === 0;

      return sendSuccess(res, 'Health check completed', {
        healthy: isHealthy,
        issues,
        stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Outbox health check failed', {
        error: error.message,
        requestedBy: req.serviceId
      });
      next(error);
    }
  }
);

module.exports = router;
